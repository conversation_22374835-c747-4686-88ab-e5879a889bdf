'use client';

import { useState } from 'react';
import { ProjectsList } from '@/components/projects-list';
import { NewProjectButton } from '@/components/new-project-button';
import { Search, Download, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface ProjectsOverviewProps {
  projects: any[];
}

export function ProjectsOverview({ projects }: ProjectsOverviewProps) {
  const [searchTerm, setSearchTerm] = useState('');

  // Filter projects based on search term
  const filteredProjects = projects.filter(project => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      (project.name && project.name.toLowerCase().includes(searchLower)) ||
      (project.location && project.location.toLowerCase().includes(searchLower)) ||
      (project.type && project.type.toLowerCase().includes(searchLower)) ||
      (project.client_name && project.client_name.toLowerCase().includes(searchLower))
    );
  });

  return (
    <div className="w-full">
      {/* Main container */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden border border-indigo-100 mb-8">
        {/* Header with title, search and actions */}
        <div className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b border-indigo-100 px-8 py-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            {/* Left side - Title and search */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-5">
              <h2 className="text-2xl font-bold text-indigo-800 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                Projects
              </h2>
              <div className="relative max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-indigo-400" />
                </div>
                <Input
                  type="text"
                  placeholder="Search by name, location, type or client..."
                  className="pl-10 pr-4 py-2 h-11 border border-indigo-200 rounded-lg w-full sm:w-80 focus:ring-indigo-400 focus:border-indigo-400 bg-white shadow-sm"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-indigo-400 hover:text-indigo-600"
                    onClick={() => setSearchTerm('')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Right side - Action buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                className="text-indigo-600 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700 transition-colors h-11 rounded-lg shadow-sm"
              >
                <Filter className="h-4 w-4 mr-2" />
                <span>Filter</span>
              </Button>
              <Button
                variant="outline"
                className="text-indigo-600 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700 transition-colors h-11 rounded-lg shadow-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                <span>Export</span>
              </Button>
              <NewProjectButton />
            </div>
          </div>
        </div>

        {/* Project count and filter indicators */}
        {searchTerm && (
          <div className="px-8 py-3 bg-gradient-to-r from-indigo-100/50 to-blue-100/50 border-b border-indigo-100 flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm text-indigo-700 font-medium">
                Found {filteredProjects.length} {filteredProjects.length === 1 ? 'project' : 'projects'} matching "{searchTerm}"
              </span>
            </div>
            <button
              className="text-xs text-indigo-600 hover:text-indigo-800 hover:underline font-medium"
              onClick={() => setSearchTerm('')}
            >
              Clear search
            </button>
          </div>
        )}

        {/* Projects list */}
        <ProjectsList projects={filteredProjects} />
      </div>
    </div>
  );
}
