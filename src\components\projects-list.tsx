'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { toast } from 'sonner';
import { MoreVertical, Archive, Trash2, ExternalLink } from 'lucide-react';
import { deleteProject, updateProject } from '@/services/project-service';
import { useRouter } from 'next/navigation';

interface ProjectsListProps {
  projects: any[];
}

export function ProjectsList({ projects }: ProjectsListProps) {
  const router = useRouter();
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isArchiving, setIsArchiving] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Maintain local state of projects for real-time updates
  const [localProjects, setLocalProjects] = useState<any[]>([]);

  // Initialize local projects from props
  useEffect(() => {
    if (Array.isArray(projects)) {
      setLocalProjects(projects);
    }
  }, [projects]);

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setActiveMenu(null);
      }
    }

    if (activeMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeMenu]);

  // Make sure we have valid projects to display
  const validProjects = localProjects.length > 0 ? localProjects : (Array.isArray(projects) ? projects : []);

  // Handle delete project
  const handleDeleteProject = async (e: React.MouseEvent, projectId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      setIsDeleting(projectId);
      try {
        const { success, error } = await deleteProject(projectId);

        if (success) {
          // Update local state immediately for real-time UI update
          setLocalProjects(prevProjects => prevProjects.filter(p => p.id !== projectId));
          toast.success('Project deleted successfully');

          // Still refresh the router to update other components
          router.refresh();
        } else {
          toast.error(`Failed to delete project: ${error?.message || 'Unknown error'}`);
        }
      } catch (err) {
        toast.error('An error occurred while deleting the project');
        console.error('Error deleting project:', err);
      } finally {
        setIsDeleting(null);
        setActiveMenu(null);
      }
    }
  };

  // Handle archive project
  const handleArchiveProject = async (e: React.MouseEvent, projectId: string, project: any) => {
    e.preventDefault();
    e.stopPropagation();

    setIsArchiving(projectId);
    try {
      const updatedProject = {
        ...project,
        status: 'Archived'
      };

      const { data, error } = await updateProject(projectId, updatedProject);

      if (data) {
        // Update local state immediately for real-time UI update
        setLocalProjects(prevProjects =>
          prevProjects.map(p => p.id === projectId ? { ...p, status: 'Archived' } : p)
        );

        toast.success('Project archived successfully');

        // Still refresh the router to update other components
        router.refresh();
      } else {
        toast.error(`Failed to archive project: ${error?.message || 'Unknown error'}`);
      }
    } catch (err) {
      toast.error('An error occurred while archiving the project');
      console.error('Error archiving project:', err);
    } finally {
      setIsArchiving(null);
      setActiveMenu(null);
    }
  };

  // Toggle menu for a project
  const toggleMenu = (e: React.MouseEvent, projectId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setActiveMenu(activeMenu === projectId ? null : projectId);
  };

  // Add sorting state
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Handle sort change
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort projects
  const sortedProjects = [...validProjects].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';

    if (sortDirection === 'asc') {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      return dateString;
    }
  };

  // Function to get vibrant color based on project name
  const getProjectColor = (name: string) => {
    // Array of vibrant colors
    const colors = [
      { bg: '#e0f2fe', text: '#0284c7' }, // Sky blue
      { bg: '#fef3c7', text: '#d97706' }, // Amber
      { bg: '#dcfce7', text: '#16a34a' }, // Green
      { bg: '#f3e8ff', text: '#9333ea' }, // Purple
      { bg: '#ffedd5', text: '#ea580c' }, // Orange
      { bg: '#dbeafe', text: '#2563eb' }, // Blue
      { bg: '#fce7f3', text: '#db2777' }, // Pink
      { bg: '#d1fae5', text: '#059669' }, // Emerald
      { bg: '#ffe4e6', text: '#e11d48' }, // Rose
      { bg: '#f5f3ff', text: '#7c3aed' }  // Violet
    ];

    // Use the first character of the name to select a color
    const charCode = (name || 'A').charCodeAt(0);
    return colors[charCode % colors.length];
  };

  return (
    <div className="overflow-hidden rounded-lg border border-indigo-100 shadow-sm">
      {/* Excel-like table layout with colorful header */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          {/* Table Header */}
          <thead>
            <tr className="bg-gradient-to-r from-indigo-50 to-blue-50 border-b border-indigo-100">
              {/* Column Headers with Sort Controls */}
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors rounded-tl-lg"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  <span>Project Name</span>
                  {sortField === 'name' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors"
                onClick={() => handleSort('location')}
              >
                <div className="flex items-center">
                  <span>Location</span>
                  {sortField === 'location' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  <span>Status</span>
                  {sortField === 'status' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors"
                onClick={() => handleSort('building_consent')}
              >
                <div className="flex items-center">
                  <span>Building Consent</span>
                  {sortField === 'building_consent' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors"
                onClick={() => handleSort('resource_consent')}
              >
                <div className="flex items-center">
                  <span>Resource Consent</span>
                  {sortField === 'resource_consent' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors"
                onClick={() => handleSort('start_date')}
              >
                <div className="flex items-center">
                  <span>Start Date</span>
                  {sortField === 'start_date' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-indigo-100/50 transition-colors"
                onClick={() => handleSort('completion_date')}
              >
                <div className="flex items-center">
                  <span>Completion</span>
                  {sortField === 'completion_date' && (
                    <span className="ml-2 text-indigo-600">
                      {sortDirection === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </div>
              </th>
              <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider rounded-tr-lg">
                Actions
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="bg-white divide-y divide-indigo-50">
            {sortedProjects.map((project, index) => {
              // Get color based on project name
              const projectColor = getProjectColor(project.name || '');

              return (
                <tr
                  key={project.id}
                  className={`hover:bg-gradient-to-r hover:from-indigo-50/30 hover:to-blue-50/30 transition-colors duration-150 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-indigo-50/10'}`}
                >
                  {/* Project Name */}
                  <td className="px-6 py-4">
                    <Link href={`/projects/${project.id}`} className="block">
                      <div className="flex items-center">
                        <div
                          className="w-12 h-12 rounded-lg flex items-center justify-center mr-3 flex-shrink-0 shadow-sm"
                          style={{
                            background: `linear-gradient(135deg, ${projectColor.bg} 0%, ${projectColor.bg}dd 100%)`,
                            color: projectColor.text
                          }}
                        >
                          <span className="text-xl font-bold">
                            {(project.name || '').charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 hover:text-indigo-600 transition-colors">
                            {project.name || 'Unnamed Project'}
                          </div>
                          {project.description && (
                            <div className="text-xs text-indigo-500 mt-0.5 line-clamp-1">
                              {project.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </Link>
                  </td>

                  {/* Location */}
                  <td className="px-6 py-4">
                    <div className="flex items-center text-sm text-indigo-700">
                      <LocationIcon className="w-3.5 h-3.5 mr-1.5 flex-shrink-0 text-indigo-400" />
                      <span>{project.location || '-'}</span>
                    </div>
                  </td>

                  {/* Status */}
                  <td className="px-6 py-4">
                    <StatusBadge status={project.status || 'Unknown'} />
                  </td>

                  {/* Building Consent */}
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">
                      {project.building_consent || '-'}
                    </div>
                  </td>

                  {/* Resource Consent */}
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-700">
                      {project.resource_consent || '-'}
                    </div>
                  </td>

                  {/* Start Date */}
                  <td className="px-6 py-4">
                    <div className="text-sm text-indigo-700">
                      {formatDate(project.start_date)}
                    </div>
                  </td>

                  {/* Completion Date */}
                  <td className="px-6 py-4">
                    <div className="text-sm text-indigo-700">
                      {formatDate(project.completion_date)}
                    </div>
                  </td>

                  {/* Actions */}
                  <td className="px-6 py-4 text-right">
                    <div className="flex items-center justify-end">
                      <button
                        className="p-1.5 rounded-full text-indigo-500 hover:text-indigo-700 hover:bg-indigo-100 transition-all"
                        onClick={(e) => toggleMenu(e, project.id)}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </button>

                      {/* Action Menu */}
                      {activeMenu === project.id && (
                        <div ref={menuRef} className="absolute mt-2 right-6 z-30 bg-white rounded-lg shadow-xl py-1.5 min-w-[180px] border border-indigo-100">
                          <Link
                            href={`/projects/${project.id}`}
                            className="flex items-center px-4 py-2.5 text-sm text-indigo-700 hover:bg-indigo-50 hover:text-indigo-800 w-full text-left transition-colors"
                          >
                            <ExternalLink className="w-4 h-4 mr-2.5" />
                            View Project
                          </Link>
                          <button
                            className="flex items-center px-4 py-2.5 text-sm text-indigo-700 hover:bg-indigo-50 hover:text-indigo-800 w-full text-left transition-colors"
                            onClick={(e) => handleArchiveProject(e, project.id, project)}
                            disabled={isArchiving === project.id}
                          >
                            <Archive className="w-4 h-4 mr-2.5" />
                            {isArchiving === project.id ? 'Archiving...' : 'Archive Project'}
                          </button>
                          <div className="border-t border-indigo-100 my-1"></div>
                          <button
                            className="flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 w-full text-left transition-colors"
                            onClick={(e) => handleDeleteProject(e, project.id)}
                            disabled={isDeleting === project.id}
                          >
                            <Trash2 className="w-4 h-4 mr-2.5" />
                            {isDeleting === project.id ? 'Deleting...' : 'Delete Project'}
                          </button>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Empty State */}
      {sortedProjects.length === 0 && (
        <div className="text-center py-16 bg-gradient-to-b from-white to-indigo-50/30">
          <div className="w-20 h-20 mx-auto mb-5 rounded-full bg-gradient-to-br from-indigo-100 to-blue-100 flex items-center justify-center shadow-md">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-indigo-900 mb-2">No projects found</h3>
          <p className="text-indigo-600 max-w-md mx-auto mb-6">Create a new project to get started or try adjusting your search criteria.</p>
          <button className="px-5 py-2.5 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full font-medium shadow-md hover:from-indigo-600 hover:to-blue-600 transition-all">
            Create New Project
          </button>
        </div>
      )}
    </div>
  );
}

// Status badge component
function StatusBadge({ status }: { status: string }) {
  let bgGradient = '';
  let textColor = '';
  let borderColor = '';
  let icon = null;

  switch (status) {
    case 'In Progress':
      bgGradient = 'bg-gradient-to-r from-blue-500/10 to-indigo-500/10';
      textColor = 'text-blue-700';
      borderColor = 'border-blue-300';
      icon = (
        <svg className="w-3.5 h-3.5 mr-1.5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      );
      break;
    case 'Planning':
      bgGradient = 'bg-gradient-to-r from-amber-500/10 to-yellow-500/10';
      textColor = 'text-amber-700';
      borderColor = 'border-amber-300';
      icon = (
        <svg className="w-3.5 h-3.5 mr-1.5 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      );
      break;
    case 'Completed':
      bgGradient = 'bg-gradient-to-r from-green-500/10 to-emerald-500/10';
      textColor = 'text-green-700';
      borderColor = 'border-green-300';
      icon = (
        <svg className="w-3.5 h-3.5 mr-1.5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
      break;
    case 'Archived':
      bgGradient = 'bg-gradient-to-r from-gray-400/10 to-slate-400/10';
      textColor = 'text-gray-600';
      borderColor = 'border-gray-300';
      icon = (
        <svg className="w-3.5 h-3.5 mr-1.5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
        </svg>
      );
      break;
    case 'On Hold':
      bgGradient = 'bg-gradient-to-r from-orange-500/10 to-red-500/10';
      textColor = 'text-orange-700';
      borderColor = 'border-orange-300';
      icon = (
        <svg className="w-3.5 h-3.5 mr-1.5 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
      break;
    default:
      bgGradient = 'bg-gradient-to-r from-purple-500/10 to-violet-500/10';
      textColor = 'text-purple-700';
      borderColor = 'border-purple-300';
      icon = (
        <svg className="w-3.5 h-3.5 mr-1.5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
  }

  return (
    <span className={`px-3 py-1.5 rounded-full text-xs font-medium flex items-center border shadow-sm ${bgGradient} ${textColor} ${borderColor}`}>
      {icon}
      {status}
    </span>
  );
}

// Location icon component
function LocationIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
      <circle cx="12" cy="10" r="3"></circle>
    </svg>
  );
}
